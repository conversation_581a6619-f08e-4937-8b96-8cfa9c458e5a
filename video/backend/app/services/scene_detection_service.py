"""
场景检测服务
使用PySceneDetect进行视频场景切换检测
"""

import os
import json
from typing import List, Dict, Optional
from loguru import logger
from sqlalchemy.orm import Session

from scenedetect import open_video, SceneManager
from scenedetect.detectors import ContentDetector, ThresholdDetector
from scenedetect.video_splitter import split_video_ffmpeg

from app.models.task import Video, SceneChange, Clip
from app.core.database import get_db
from app.utils.file_organization import VideoFileOrganizer


class SceneDetectionService:
    """场景检测服务"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def detect_scenes(
        self,
        video_id: int,
        detector_type: str = "content",
        threshold: float = 30.0,
        min_scene_len: float = 1.0,
        save_images: bool = False,
        output_dir: Optional[str] = None
    ) -> List[Dict]:
        """
        检测视频场景切换
        
        Args:
            video_id: 视频ID
            detector_type: 检测器类型 https://www.scenedetect.com/docs/latest/cli.html#detectors
            threshold: 检测阈值
            min_scene_len: 最小场景长度（秒）
            save_images: 是否保存场景图片
            output_dir: 输出目录
            
        Returns:
            List[Dict]: 场景列表
        """
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        if not os.path.exists(video.file_path):
            raise FileNotFoundError(f"Video file not found: {video.file_path}")
        
        try:
            logger.info(f"Starting scene detection for video {video_id}")

            # 打开视频文件
            video = open_video(video.file_path)
            scene_manager = SceneManager()

            # 选择检测器
            if detector_type == "content":
                detector = ContentDetector(threshold=threshold)
            elif detector_type == "threshold":
                detector = ThresholdDetector(threshold=threshold)
            else:
                raise ValueError(f"Unsupported detector type: {detector_type}")

            scene_manager.add_detector(detector)

            # 检测场景
            scene_manager.detect_scenes(video, show_progress=True)

            # 获取场景列表
            scene_list = scene_manager.get_scene_list()
            
            logger.info(f"Detected {len(scene_list)} scenes for video {video_id}")
            
            # 转换为字典格式
            scenes = []
            for i, (start_time, end_time) in enumerate(scene_list):
                scene_data = {
                    "scene_number": i + 1,
                    "start_time": start_time.get_seconds(),
                    "end_time": end_time.get_seconds(),
                    "duration": (end_time - start_time).get_seconds(),
                    "start_frame": start_time.get_frames(),
                    "end_frame": end_time.get_frames(),
                    "confidence": self._calculate_confidence(detector_type, threshold)
                }
                scenes.append(scene_data)
            
            # 保存到数据库
            self._save_scenes_to_db(video_id, scenes, detector_type, threshold)
            
            # 保存场景图片（可选）
            if save_images and output_dir:
                self._save_scene_images(video.file_path, scene_list, output_dir)
            
            logger.info(f"Scene detection completed for video {video_id}")
            return scenes
            
        except Exception as e:
            logger.error(f"Failed to detect scenes for video {video_id}: {e}")
            raise
        finally:
            # 新版本的PySceneDetect不需要手动释放资源
            pass
    
    def _calculate_confidence(self, detector_type: str, threshold: float) -> float:
        """计算检测置信度"""
        if detector_type == "content":
            # Content detector的置信度基于阈值
            # 阈值越高，检测越严格，置信度越高
            return min(0.95, 0.5 + (threshold / 100.0))
        elif detector_type == "threshold":
            # Threshold detector的置信度相对固定
            return 0.8
        else:
            return 0.7
    
    def _save_scenes_to_db(
        self,
        video_id: int,
        scenes: List[Dict],
        detector_type: str,
        threshold: float
    ):
        """保存场景到数据库"""
        try:
            # 删除现有的场景记录
            self.db.query(SceneChange).filter(SceneChange.video_id == video_id).delete()
            
            # 添加新的场景记录
            for scene in scenes:
                scene_change = SceneChange(
                    video_id=video_id,
                    scene_number=scene["scene_number"],
                    start_time=scene["start_time"],
                    end_time=scene["end_time"],
                    duration=scene["duration"],
                    start_frame=scene["start_frame"],
                    end_frame=scene["end_frame"],
                    confidence=scene["confidence"],
                    detector_type=detector_type,
                    threshold=threshold,
                    scene_metadata=json.dumps({
                        "detector_type": detector_type,
                        "threshold": threshold,
                        "total_scenes": len(scenes)
                    })
                )
                self.db.add(scene_change)
            
            self.db.commit()
            logger.info(f"Saved {len(scenes)} scenes to database for video {video_id}")
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to save scenes to database: {e}")
            raise
    
    def _save_scene_images(
        self,
        video_path: str,
        scene_list: List,
        output_dir: str
    ):
        """保存场景图片"""
        try:
            os.makedirs(output_dir, exist_ok=True)

            # 使用OpenCV直接保存场景图片
            import cv2

            cap = cv2.VideoCapture(video_path)
            fps = cap.get(cv2.CAP_PROP_FPS)

            for i, (start_time, end_time) in enumerate(scene_list):
                # 计算开始帧号
                start_frame = int(start_time.get_seconds() * fps)

                # 跳转到开始帧
                cap.set(cv2.CAP_PROP_POS_FRAMES, start_frame)
                ret, frame = cap.read()

                if ret:
                    image_path = os.path.join(output_dir, f"scene_{i+1:03d}_start.jpg")
                    cv2.imwrite(image_path, frame)
                    logger.debug(f"Saved scene image: {image_path}")

            cap.release()

        except Exception as e:
            logger.warning(f"Failed to save scene images: {e}")
    
    def get_scenes_summary(self, video_id: int) -> Dict:
        """获取场景检测摘要"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")
        
        scenes = self.db.query(SceneChange).filter(
            SceneChange.video_id == video_id
        ).order_by(SceneChange.scene_number).all()
        
        if not scenes:
            return {
                "video_id": video_id,
                "total_scenes": 0,
                "scenes": []
            }
        
        # 计算统计信息
        total_scenes = len(scenes)
        avg_scene_duration = sum(scene.duration for scene in scenes) / total_scenes
        min_scene_duration = min(scene.duration for scene in scenes)
        max_scene_duration = max(scene.duration for scene in scenes)
        
        return {
            "video_id": video_id,
            "total_scenes": total_scenes,
            "avg_scene_duration": avg_scene_duration,
            "min_scene_duration": min_scene_duration,
            "max_scene_duration": max_scene_duration,
            "detector_type": scenes[0].detector_type if scenes else None,
            "threshold": scenes[0].threshold if scenes else None,
            "scenes": [
                {
                    "scene_number": scene.scene_number,
                    "start_time": scene.start_time,
                    "end_time": scene.end_time,
                    "duration": scene.duration,
                    "confidence": scene.confidence
                }
                for scene in scenes
            ]
        }
    
    def split_video_by_scenes(
        self,
        video_id: int,
        output_format: str = "mp4"
    ) -> List[Dict]:
        """根据场景切分视频并保存到标准化目录结构"""
        video = self.db.query(Video).filter(Video.id == video_id).first()
        if not video:
            raise ValueError(f"Video {video_id} not found")

        scenes = self.db.query(SceneChange).filter(
            SceneChange.video_id == video_id
        ).order_by(SceneChange.scene_number).all()

        if not scenes:
            logger.warning(f"No scenes found for video {video_id}, attempting to detect scenes first")
            # 如果没有场景数据，先进行场景检测
            self.detect_scenes(video_id)
            scenes = self.db.query(SceneChange).filter(
                SceneChange.video_id == video_id
            ).order_by(SceneChange.scene_number).all()

            if not scenes:
                raise ValueError(f"No scenes found for video {video_id} even after detection")

        try:
            # 使用VideoFileOrganizer管理文件路径
            file_organizer = VideoFileOrganizer()
            scenes_dir = file_organizer.get_video_subdirectory(video_id, 'scenes')

            # 构建场景时间列表
            from scenedetect.frame_timecode import FrameTimecode

            # 获取视频帧率
            import cv2
            cap = cv2.VideoCapture(video.file_path)
            fps = cap.get(cv2.CAP_PROP_FPS)
            cap.release()

            scene_list = []
            for scene in scenes:
                start_tc = FrameTimecode(scene.start_time, fps=fps)
                end_tc = FrameTimecode(scene.end_time, fps=fps)
                scene_list.append((start_tc, end_tc))

            # 使用ffmpeg切分视频
            output_files = split_video_ffmpeg(
                input_video_paths=[video.file_path],
                scene_list=scene_list,
                output_file_template=str(scenes_dir / f"scene_$SCENE_NUMBER.{output_format}"),
                video_name=os.path.splitext(video.filename)[0],
                arg_override="-c copy",  # 使用copy模式加快速度
                show_progress=True
            )

            # 将分割后的文件信息保存到数据库
            scene_clips = self._save_scene_clips_to_db(video_id, scenes, output_files, output_format)

            logger.info(f"Split video {video_id} into {len(output_files)} scene files and saved to database")
            return scene_clips

        except Exception as e:
            logger.error(f"Failed to split video by scenes: {e}")
            raise

    def _save_scene_clips_to_db(
        self,
        video_id: int,
        scenes: List[SceneChange],
        output_files: List[str],
        output_format: str
    ) -> List[Dict]:
        """将场景片段信息保存到数据库"""
        try:
            scene_clips = []

            # 删除现有的场景片段记录
            self.db.query(Clip).filter(
                Clip.video_id == video_id,
                Clip.clip_type == "scene"
            ).delete()

            # 确保output_files和scenes数量匹配
            if len(output_files) != len(scenes):
                logger.warning(f"Output files count ({len(output_files)}) doesn't match scenes count ({len(scenes)})")

            for scene in scenes:
                # 构建预期的文件路径
                file_organizer = VideoFileOrganizer()
                expected_file_path = file_organizer.get_scene_file_path(video_id, scene.scene_number, output_format)

                # 检查文件是否存在
                if not os.path.exists(expected_file_path):
                    logger.warning(f"Scene file not found: {expected_file_path}")
                    continue

                # 创建Clip记录
                clip = Clip(
                    video_id=video_id,
                    start_time=scene.start_time,
                    end_time=scene.end_time,
                    duration=scene.duration,
                    clip_type="scene",
                    title=f"场景 {scene.scene_number}",
                    description=f"自动分割的场景片段 {scene.scene_number}",
                    file_path=str(expected_file_path),
                    clip_metadata=json.dumps({
                        "scene_number": scene.scene_number,
                        "detector_type": scene.detector_type,
                        "threshold": scene.threshold,
                        "confidence": scene.confidence,
                        "start_frame": scene.start_frame,
                        "end_frame": scene.end_frame,
                        "output_format": output_format
                    })
                )
                self.db.add(clip)

                # 构建返回数据
                scene_clips.append({
                    "scene_number": scene.scene_number,
                    "start_time": scene.start_time,
                    "end_time": scene.end_time,
                    "duration": scene.duration,
                    "file_path": str(expected_file_path),
                    "title": f"场景 {scene.scene_number}",
                    "confidence": scene.confidence
                })

            self.db.commit()
            logger.info(f"Saved {len(scene_clips)} scene clips to database for video {video_id}")
            return scene_clips

        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to save scene clips to database: {e}")
            raise
